<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mainos</title>
    <link
      rel="stylesheet"
      href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
      integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
      crossorigin=""
    />
    <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.css" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.Default.css" />
    <link rel="stylesheet" href="styles.css" />

    <!-- https://dexie.org/docs/Tutorial/Hello-World -->
    <script src="https://unpkg.com/dexie/dist/dexie.js"></script>
    
    <!-- https://counter.dev/dashboard.html -->
    <script src="https://cdn.counter.dev/script.js" data-id="7eb64be9-4464-4de5-ba46-f3245c72bc8c" data-utcoffset="3"></script>
</head>
<body>
  <div id="map">
    <div id="container">
      <div class="header">
        <h3 id="tracker-title">Location Tracker</h3>
        <button id="minimize-btn" class="minimize-btn" onclick="toggleMinimize()">
          <i class="fas fa-minus"></i>
        </button>
      </div>
      <div id="tracker-content">
        <div class="progress-stats-container">
          <div class="progress-section">
            <div class="progress-header">
              <span>Progress:</span>
              <span id="progress-percent">0%</span>
            </div>
            <div class="progress-bar-container">
              <div id="progress-bar"></div>
            </div>
          </div>
          <div class="stats-compact">
            <div class="stats-row-compact">
              <span>Total: <span id="total-count">0</span></span>
              <span class="visited-text">Visited: <span id="visited-count" class="visited-count">0</span></span>
              <span class="not-visited-text">Not Visited: <span id="not-visited-count" class="not-visited-count">0</span></span>
            </div>
          </div>
        </div>
        <div class="city-filter-section">
          <label for="city-filter-dropdown">Filter by City:</label>
          <select id="city-filter-dropdown" onchange="applyCityFilter(this.value)" class="city-filter-select">
            <option value="">All Cities</option>
          </select>
        </div>
        <div id="campaign-info" class="campaign-info" style="display: none;">
        </div>
        <div class="grey-markers-toggle">
          <label class="checkbox-label">
            <input
              type="checkbox"
              id="grey-markers-toggle"
              checked
              onchange="saveStateAndRender()"
              class="checkbox-input"
            />
            <span>Base markers</span>
          </label>
          <button onclick="refreshAllLocationData()" class="btn btn-refresh" title="Reset base markers">
            <i class="fas fa-sync-alt"></i> Reset
          </button>
          <button onclick="toggleLegend()" class="btn btn-legend" title="Show/Hide marker legend">
            <i class="fas fa-map-marker-alt"></i> Legend
          </button>
        </div>
        <div id="marker-legend" class="marker-legend" style="display: none;">
          <div class="legend-header">
            <span>Marker Types</span>
          </div>
          <div class="legend-items">
            <div class="legend-item">
              <div class="legend-marker" id="legend-maxi"></div>
              <span>Maxi</span>
            </div>
            <div class="legend-item">
              <div class="legend-marker" id="legend-classic-keski"></div>
              <span>Classic Keski</span>
            </div>
            <div class="legend-item">
              <div class="legend-marker" id="legend-classic-single"></div>
              <span>Classic Single</span>
            </div>
          </div>
          <div class="legend-colors">
            <div class="legend-color-item">
              <div class="legend-color-marker" style="background-color: #7B7B7B;"></div>
              <span>Base locations (grey)</span>
            </div>
            <div class="legend-color-item">
              <div class="legend-color-marker" style="background-color: #CB2B3E;"></div>
              <span>Campaign locations (any color)</span>
            </div>
            <div class="legend-color-item">
              <div class="legend-color-marker" style="background-color: #2AAD27;"></div>
              <span>Visited locations (green)</span>
            </div>
          </div>
        </div>
        <div class="campaign-input-section">
          <div class="input-group">
            <input type="text" id="campaign-id-input" class="campaign-input" placeholder="Campaign ID">
            <button onclick="loadCampaignData(event)" class="btn btn-warning">Load</button>
          </div>
        </div>
        <div class="button-container">
          <button onclick="locateUser()" class="btn btn-info" title="Find My Location">
            <i class="fas fa-crosshairs"></i> Find Me
          </button>
          <button onclick="exportData()" class="btn btn-primary">Export</button>
          <label for="import-file" class="btn btn-success">
            Import
            <input type="file" id="import-file" accept=".json" onchange="importData(event)">
          </label>
        </div>
        <div class="control-section">
          <div class="clustering-toggle">
            <label class="checkbox-label">
              <input
                type="checkbox"
                id="clustering-toggle"
                checked
                onchange="toggleClustering(this.checked)"
                class="checkbox-input"
              />
              <span>Clustering markers</span>
            </label>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script src="https://kit.fontawesome.com/91c7a109fb.js" crossorigin="anonymous"></script>
  <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
    integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
    crossorigin=""></script>
  <script src="https://unpkg.com/leaflet.markercluster@1.4.1/dist/leaflet.markercluster.js"></script>
  <script src="script.js"></script>
</body>
</html>