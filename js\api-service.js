/**
 * API Service for handling all API operations
 */

import { API_CONFIG, buildApiUrl, buildCampaignUrl } from './constants.js';

/**
 * Handles all API operations for fetching location and campaign data
 */
export class ApiService {
    constructor() {
        this.currentPage = API_CONFIG.INITIAL_PAGE;
        this.currentApiUrl = buildApiUrl(this.currentPage);
    }

    /**
     * Fetches a single page of location data from the base API
     * @returns {Promise<Object>} API response data
     * @throws {Error} When API request fails
     */
    async fetchLocationDataPage() {
        try {
            const response = await fetch(this.currentApiUrl);
            if (!response.ok) {
                throw new Error(`API request failed with status: ${response.status}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error fetching location data:', error);
            throw error;
        }
    }

    /**
     * Fetches all pages of location data recursively
     * @returns {Promise<Array>} All location data
     */
    async fetchAllLocationData() {
        const allData = [];
        let hasMorePages = true;

        while (hasMorePages) {
            try {
                const pageData = await this.fetchLocationDataPage();
                allData.push(...pageData.results);

                if (pageData.next) {
                    this.currentPage++;
                    this.currentApiUrl = buildApiUrl(this.currentPage);
                } else {
                    hasMorePages = false;
                }
            } catch (error) {
                console.error('Error fetching page data:', error);
                throw error;
            }
        }

        return allData;
    }

    /**
     * Fetches campaign data for a specific campaign ID
     * @param {string} campaignId - The campaign ID to fetch
     * @returns {Promise<Object>} Campaign data
     * @throws {Error} When campaign data fetch fails
     */
    async fetchCampaignData(campaignId) {
        if (!campaignId) {
            throw new Error('Campaign ID is required');
        }

        const campaignUrl = buildCampaignUrl(campaignId);
        if (!campaignUrl) {
            throw new Error('Failed to build campaign URL');
        }

        try {
            const response = await fetch(campaignUrl);
            if (!response.ok) {
                throw new Error(`Failed to fetch campaign data. Status: ${response.status}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error fetching campaign data:', error);
            throw error;
        }
    }

    /**
     * Resets the API service to initial state
     */
    reset() {
        this.currentPage = API_CONFIG.INITIAL_PAGE;
        this.currentApiUrl = buildApiUrl(this.currentPage);
    }
}

/**
 * Extracts marker data from campaign API response
 * @param {Object} campaignData - Raw campaign data from API
 * @returns {Array} Array of processed campaign markers
 */
export function extractCampaignMarkers(campaignData) {
    const campaignMarkers = [];
    
    if (!campaignData.reserved_resources) {
        return campaignMarkers;
    }
    
    campaignData.reserved_resources.forEach(resource => {
        if (resource.inventory_resource && resource.inventory_resource.map_point_markers) {
            resource.inventory_resource.map_point_markers.forEach(marker => {
                campaignMarkers.push({
                    campaignId: campaignData.id,
                    markerId: marker.id,
                    markerName: marker.name,
                    markerLat: marker.lat,
                    markerLng: marker.lng,
                    campaignStartDate: resource.start_date,
                    campaignEndDate: resource.end_date,
                    campaignName: campaignData.name,
                    campaignDescription: campaignData.description,
                    markerVisited: false,
                    markerDateVisited: null
                });
            });
        }
    });
    
    return campaignMarkers;
}
