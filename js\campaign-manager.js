/**
 * Campaign Management System
 */

import { CAMPAIGN_COLORS, MARKER_COLORS, STORAGE_KEYS } from './constants.js';
import { safeJsonParse, safeJsonStringify } from './utilities.js';

/**
 * Manages campaign settings, colors, and visibility
 */
export class CampaignManager {
    constructor() {
        this.campaigns = new Map();
        this.nextColorIndex = 0;
        this.loadCampaignSettings();
    }

    /**
     * Adds a new campaign or updates an existing one
     * @param {string} campaignId - The campaign ID
     * @param {string} campaignName - The campaign name
     * @param {string} campaignDescription - The campaign description
     */
    addCampaign(campaignId, campaignName, campaignDescription = '') {
        if (!this.campaigns.has(campaignId)) {
            const assignedColor = this.getNextAvailableColor();
            this.campaigns.set(campaignId, {
                name: campaignName,
                description: campaignDescription,
                color: assignedColor,
                visible: true
            });
        } else {
            this.updateExistingCampaign(campaignId, campaignName, campaignDescription);
        }
        this.saveCampaignSettings();
    }

    /**
     * Updates an existing campaign's information
     * @param {string} campaignId - The campaign ID
     * @param {string} campaignName - The campaign name
     * @param {string} campaignDescription - The campaign description
     */
    updateExistingCampaign(campaignId, campaignName, campaignDescription) {
        const campaign = this.campaigns.get(campaignId);
        if (campaign) {
            campaign.name = campaignName;
            campaign.description = campaignDescription;
        }
    }

    /**
     * Gets the next available color for a new campaign
     * @returns {string} Color hex code
     */
    getNextAvailableColor() {
        const color = CAMPAIGN_COLORS[this.nextColorIndex % CAMPAIGN_COLORS.length];
        this.nextColorIndex++;
        return color;
    }

    /**
     * Removes a campaign
     * @param {string} campaignId - The campaign ID to remove
     */
    removeCampaign(campaignId) {
        this.campaigns.delete(campaignId);
        this.saveCampaignSettings();
    }

    /**
     * Gets campaign color
     * @param {string} campaignId - The campaign ID
     * @returns {string} The campaign color or default grey
     */
    getCampaignColor(campaignId) {
        if (!campaignId) return MARKER_COLORS.GREY;
        const campaign = this.campaigns.get(campaignId);
        return campaign ? campaign.color : MARKER_COLORS.GREY;
    }

    /**
     * Gets campaign information
     * @param {string} campaignId - The campaign ID
     * @returns {Object|null} Campaign information or null if not found
     */
    getCampaignInfo(campaignId) {
        return this.campaigns.get(campaignId) || null;
    }

    /**
     * Checks if a campaign exists
     * @param {string} campaignId - The campaign ID
     * @returns {boolean} True if campaign exists
     */
    hasCampaign(campaignId) {
        return this.campaigns.has(campaignId);
    }

    /**
     * Sets campaign visibility
     * @param {string} campaignId - The campaign ID
     * @param {boolean} isVisible - Whether the campaign should be visible
     */
    setCampaignVisibility(campaignId, isVisible) {
        const campaign = this.campaigns.get(campaignId);
        if (campaign) {
            campaign.visible = isVisible;
            this.saveCampaignSettings();
        }
    }

    /**
     * Toggles campaign visibility
     * @param {string} campaignId - The campaign ID
     * @returns {boolean} New visibility state
     */
    toggleCampaignVisibility(campaignId) {
        const campaign = this.campaigns.get(campaignId);
        if (campaign) {
            campaign.visible = !campaign.visible;
            this.saveCampaignSettings();
            return campaign.visible;
        }
        return false;
    }

    /**
     * Gets the total number of campaigns
     * @returns {number} Number of campaigns
     */
    getCampaignCount() {
        return this.campaigns.size;
    }

    /**
     * Gets all campaigns
     * @returns {Map} Map of all campaigns
     */
    getAllCampaigns() {
        return this.campaigns;
    }

    /**
     * Gets visible campaign IDs
     * @returns {Array} Array of visible campaign IDs
     */
    getVisibleCampaigns() {
        const visibleCampaigns = [];
        this.campaigns.forEach((campaign, campaignId) => {
            if (campaign.visible) {
                visibleCampaigns.push(campaignId);
            }
        });
        return visibleCampaigns;
    }

    /**
     * Checks if a campaign is visible
     * @param {string} campaignId - The campaign ID
     * @returns {boolean} True if campaign is visible
     */
    isCampaignVisible(campaignId) {
        const campaign = this.campaigns.get(campaignId);
        return campaign ? campaign.visible : false;
    }

    /**
     * Saves campaign settings to localStorage
     */
    saveCampaignSettings() {
        const campaignData = {};
        this.campaigns.forEach((campaign, campaignId) => {
            campaignData[campaignId] = campaign;
        });
        
        const settingsData = {
            campaigns: campaignData,
            nextColorIndex: this.nextColorIndex
        };
        
        localStorage.setItem(STORAGE_KEYS.CAMPAIGN_SETTINGS, safeJsonStringify(settingsData));
    }

    /**
     * Loads campaign settings from localStorage
     */
    loadCampaignSettings() {
        const savedSettings = localStorage.getItem(STORAGE_KEYS.CAMPAIGN_SETTINGS);
        if (savedSettings) {
            const parsedData = safeJsonParse(savedSettings, {});
            this.nextColorIndex = parsedData.nextColorIndex || 0;
            if (parsedData.campaigns) {
                Object.entries(parsedData.campaigns).forEach(([campaignId, campaign]) => {
                    this.campaigns.set(campaignId, campaign);
                });
            }
        }
    }

    /**
     * Clears all campaign settings
     */
    clearAllCampaigns() {
        this.campaigns.clear();
        this.nextColorIndex = 0;
        this.saveCampaignSettings();
    }

    /**
     * Exports campaign settings
     * @returns {Object} Campaign settings data
     */
    exportCampaignSettings() {
        const campaignData = {};
        this.campaigns.forEach((campaign, campaignId) => {
            campaignData[campaignId] = campaign;
        });
        
        return {
            campaigns: campaignData,
            nextColorIndex: this.nextColorIndex
        };
    }

    /**
     * Imports campaign settings
     * @param {Object} settingsData - Campaign settings data to import
     */
    importCampaignSettings(settingsData) {
        this.campaigns.clear();
        this.nextColorIndex = settingsData.nextColorIndex || 0;
        
        if (settingsData.campaigns) {
            Object.entries(settingsData.campaigns).forEach(([campaignId, campaign]) => {
                this.campaigns.set(campaignId, campaign);
            });
        }
        
        this.saveCampaignSettings();
    }
}
