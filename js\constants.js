/**
 * Application constants and configuration
 */

// API Configuration
export const API_CONFIG = {
    BASE_URL: 'https://atlasmedia.mediani.fi/api/v1/public-map-point-markers/',
    CAMPAIGN_URL_TEMPLATE: 'https://atlasmedia.mediani.fi/api/v1/reservation-resources-map/',
    CORS_PROXY_URL: 'https://corsproxy.io/?',
    DEFAULT_LOCATION_ID: '100',
    JSON_FORMAT_PARAM: '/?format=json&page=',
    INITIAL_PAGE: 1
};

// Map Configuration
export const MAP_CONFIG = {
    DEFAULT_CENTER: [62.160871, 25.6416672],
    DEFAULT_ZOOM: 8,
    DEFAULT_CLUSTER_RADIUS: 70
};

// Local Storage Keys
export const STORAGE_KEYS = {
    CAMPAIGN_SETTINGS: 'campaignSettings',
    MAP_STATE: 'mapState',
    FILTER_STATE: 'filterState'
};

// Database Configuration
export const DATABASE_CONFIG = {
    NAME: 'MainosDB',
    VERSION: 1,
    TABLES: {
        ALL_MARKERS: 'allMarkers',
        CAMPAIGN_MARKERS: 'markersCampaigns'
    }
};

// Marker Colors
export const MARKER_COLORS = {
    GREY: '#7B7B7B',
    GREEN: '#2AAD27',
    RED: '#CB2B3E',
    BLUE: '#2A81CB',
    ORANGE: '#CB8427',
    YELLOW: '#CAC428',
    VIOLET: '#9C2BCB',
    GOLD: '#FFD326',
    PINK: '#e83e8c',
    CYAN: '#17a2b8',
    LIME: '#32CD32',
    BROWN: '#8B4513',
    NAVY: '#000080',
    TEAL: '#20c997',
    SILVER: '#C0C0C0',
    MAROON: '#800000',
    OLIVE: '#808000',
    AQUA: '#00FFFF',
    FUCHSIA: '#FF00FF',
    PURPLE: '#6f42c1',
    INDIGO: '#6610f2',
    CORAL: '#FF7F50',
    CRIMSON: '#DC143C',
    FOREST: '#228B22',
    ROYAL: '#4169E1',
    TOMATO: '#FF6347',
    STEEL: '#4682B4',
    // Reserved colors (not available for campaigns)
    BLACK: '#000000',
    DARK_GREY: '#6c757d'
};

// Available colors for campaigns (excluding reserved colors)
export const CAMPAIGN_COLORS = [
    MARKER_COLORS.RED,
    MARKER_COLORS.BLUE,
    MARKER_COLORS.ORANGE,
    MARKER_COLORS.YELLOW,
    MARKER_COLORS.VIOLET,
    MARKER_COLORS.GOLD,
    MARKER_COLORS.CYAN,
    MARKER_COLORS.PINK,
    MARKER_COLORS.TEAL,
    MARKER_COLORS.INDIGO,
    MARKER_COLORS.PURPLE,
    MARKER_COLORS.BROWN,
    MARKER_COLORS.LIME,
    MARKER_COLORS.CORAL,
    MARKER_COLORS.NAVY,
    MARKER_COLORS.MAROON,
    MARKER_COLORS.OLIVE,
    MARKER_COLORS.AQUA,
    MARKER_COLORS.FUCHSIA,
    MARKER_COLORS.SILVER
];

// Advertisement Types
export const ADVERTISEMENT_TYPES = {
    MAXI: 'maxi',
    CLASSIC_KESKI: 'classic_keski', 
    CLASSIC_SINGLE: 'classic_single',
    DEFAULT: 'default'
};

// User Interface Messages
export const UI_MESSAGES = {
    CONFIRM_CLEAR_CAMPAIGN: 'Are you sure you want to clear campaign "{name}"? This action cannot be undone.',
    CONFIRM_REFRESH_DATA: 'Are you sure you want to refresh base marker data? This will clear all local data about base locations and refetch from the server.',
    CONFIRM_IMPORT_DATA: 'Are you sure you want to import data? This will completely replace all existing data. This action cannot be undone.',
    CONFIRM_OVERWRITE_CAMPAIGN: 'Campaign "{name}" (ID: {id}) already exists in the database.\nDo you want to overwrite the existing data for this campaign?',
    ERROR_LOADING_CAMPAIGN: 'Error loading campaign data. Please try again.',
    ERROR_CLEARING_CAMPAIGN: 'Error clearing campaign data. Please try again.',
    ERROR_REFRESHING_DATA: 'Error refreshing data. Please try again.',
    ERROR_IMPORTING_DATA: 'Error importing data. Please try again.',
    ERROR_EXPORTING_DATA: 'Error exporting data. Please try again.',
    CAMPAIGN_LOADED_SUCCESS: 'Campaign data loaded successfully',
    CAMPAIGN_CLEARED_SUCCESS: 'Campaign {id} cleared',
    DATA_REFRESHED_SUCCESS: 'Base marker locations refreshed successfully'
};

// Helper functions for building URLs
export function buildApiUrl(page) {
    const baseUrl = API_CONFIG.BASE_URL + API_CONFIG.DEFAULT_LOCATION_ID + API_CONFIG.JSON_FORMAT_PARAM + page;
    return API_CONFIG.CORS_PROXY_URL + encodeURIComponent(baseUrl);
}

export function buildCampaignUrl(campaignId) {
    if (!campaignId) return null;
    const baseUrl = API_CONFIG.CAMPAIGN_URL_TEMPLATE + campaignId + '/?format=json';
    return API_CONFIG.CORS_PROXY_URL + encodeURIComponent(baseUrl);
}
