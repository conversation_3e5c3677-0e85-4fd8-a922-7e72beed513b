/**
 * Data Management Classes for IndexedDB operations
 */

import { DATABASE_CONFIG } from './constants.js';

// Initialize IndexedDB database and tables
export const database = new Dexie(DATABASE_CONFIG.NAME);
database.version(DATABASE_CONFIG.VERSION).stores({
  [DATABASE_CONFIG.TABLES.ALL_MARKERS]: 'id, name, lat, lng',
  [DATABASE_CONFIG.TABLES.CAMPAIGN_MARKERS]: '[campaignId+markerId], campaignName, campaignDescription, campaignStartDate, campaignEndDate, markerName, markerLat, markerLng, markerVisited, markerDateVisited'
});

/**
 * Manages marker data operations using IndexedDB for storage.
 * Handles status tracking, timestamps, and campaign-specific data.
 */
export class MarkerDataManager {
    constructor() {
        this.database = database;
    }

    /**
     * Gets the visited status for a campaign marker
     * @param {string} campaignId - The campaign ID
     * @param {number} markerId - The marker ID
     * @returns {Promise<boolean>} The visited status
     */
    async getMarkerVisitedStatus(campaignId, markerId) {
        try {
            const marker = await this.database.markersCampaigns.get([campaignId, markerId]);
            return marker ? marker.markerVisited : false;
        } catch (error) {
            console.error('Error getting marker status:', error);
            return false;
        }
    }

    /**
     * Gets the visited timestamp for a campaign marker
     * @param {string} campaignId - The campaign ID
     * @param {number} markerId - The marker ID
     * @returns {Promise<string|null>} The visited timestamp or null
     */
    async getMarkerVisitedTimestamp(campaignId, markerId) {
        try {
            const marker = await this.database.markersCampaigns.get([campaignId, markerId]);
            return marker ? marker.markerDateVisited : null;
        } catch (error) {
            console.error('Error getting marker timestamp:', error);
            return null;
        }
    }

    /**
     * Updates the visited status of a campaign marker
     * @param {string} campaignId - The campaign ID
     * @param {number} markerId - The marker ID
     * @param {boolean} isVisited - Whether the marker is visited
     * @returns {Promise<void>}
     */
    async updateMarkerVisitedStatus(campaignId, markerId, isVisited) {
        try {
            const updateData = {
                markerVisited: isVisited,
                markerDateVisited: isVisited ? new Date().toISOString() : null
            };
            await this.database.markersCampaigns.update([campaignId, markerId], updateData);
        } catch (error) {
            console.error('Error updating marker status:', error);
        }
    }

    /**
     * Gets all campaign markers for a specific campaign
     * @param {string} campaignId - The campaign ID
     * @returns {Promise<Array>} Array of campaign markers
     */
    async getCampaignMarkers(campaignId) {
        try {
            return await this.database.markersCampaigns.where('campaignId').equals(campaignId).toArray();
        } catch (error) {
            console.error('Error getting campaign markers:', error);
            return [];
        }
    }

    /**
     * Gets all unique campaign IDs
     * @returns {Promise<Array>} Array of unique campaign IDs
     */
    async getAllCampaignIds() {
        try {
            const campaigns = await this.database.markersCampaigns.toArray();
            const uniqueIds = [...new Set(campaigns.map(c => c.campaignId))];
            return uniqueIds;
        } catch (error) {
            console.error('Error getting campaign IDs:', error);
            return [];
        }
    }

    /**
     * Clears all markers for a specific campaign
     * @param {string} campaignId - The campaign ID to clear
     * @returns {Promise<void>}
     */
    async clearCampaignData(campaignId) {
        try {
            await this.database.markersCampaigns.where('campaignId').equals(campaignId).delete();
        } catch (error) {
            console.error('Error clearing campaign data:', error);
        }
    }

    /**
     * Clears all campaign data
     * @returns {Promise<void>}
     */
    async clearAllCampaignData() {
        try {
            await this.database.markersCampaigns.clear();
        } catch (error) {
            console.error('Error clearing all campaign data:', error);
        }
    }

    /**
     * Adds campaign markers to the database
     * @param {Array} campaignMarkers - Array of campaign marker objects
     * @returns {Promise<void>}
     */
    async addCampaignMarkers(campaignMarkers) {
        try {
            await this.database.markersCampaigns.bulkAdd(campaignMarkers);
        } catch (error) {
            console.error('Error adding campaign markers:', error);
            throw error;
        }
    }

    /**
     * Adds location markers to the database
     * @param {Array} locationMarkers - Array of location marker objects
     * @returns {Promise<void>}
     */
    async addLocationMarkers(locationMarkers) {
        try {
            await this.database.allMarkers.bulkAdd(locationMarkers);
        } catch (error) {
            console.error('Error adding location markers:', error);
            throw error;
        }
    }

    /**
     * Gets all location markers
     * @returns {Promise<Array>} Array of all location markers
     */
    async getAllLocationMarkers() {
        try {
            return await this.database.allMarkers.toArray();
        } catch (error) {
            console.error('Error getting all location markers:', error);
            return [];
        }
    }

    /**
     * Gets all campaign markers
     * @returns {Promise<Array>} Array of all campaign markers
     */
    async getAllCampaignMarkers() {
        try {
            return await this.database.markersCampaigns.toArray();
        } catch (error) {
            console.error('Error getting all campaign markers:', error);
            return [];
        }
    }

    /**
     * Clears all location markers
     * @returns {Promise<void>}
     */
    async clearAllLocationMarkers() {
        try {
            await this.database.allMarkers.clear();
        } catch (error) {
            console.error('Error clearing all location markers:', error);
        }
    }

    /**
     * Gets the count of location markers
     * @returns {Promise<number>} Number of location markers
     */
    async getLocationMarkerCount() {
        try {
            return await this.database.allMarkers.count();
        } catch (error) {
            console.error('Error getting location marker count:', error);
            return 0;
        }
    }
}
