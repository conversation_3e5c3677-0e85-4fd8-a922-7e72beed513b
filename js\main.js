/**
 * Main application entry point
 * Initializes all modules and sets up the application
 */

import { MAP_CONFIG, STORAGE_KEYS, UI_MESSAGES } from './constants.js';
import { validateCampaignId, validateClusterRadius, setLoadingState, formatDate, formatTimestamp } from './utilities.js';
import { ApiService, extractCampaignMarkers } from './api-service.js';
import { MarkerDataManager, database } from './data-manager.js';
import { CampaignManager } from './campaign-manager.js';

// Global application state
let allLocationData = [];
let filteredLocationData = [];
let allCampaignData = [];
let filteredCampaignData = [];

let activeCampaignId = null;
let currentCampaignUrl = null;

// Initialize managers and services
const apiService = new ApiService();
const dataManager = new MarkerDataManager();
const campaignManager = new CampaignManager();

// Initialize map
const savedMapState = loadMapState();
const map = L.map('map').setView(
    savedMapState ? savedMapState.center : MAP_CONFIG.DEFAULT_CENTER,
    savedMapState ? savedMapState.zoom : MAP_CONFIG.DEFAULT_ZOOM
);

// Add tile layer
L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
    attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
}).addTo(map);

// Save map state on changes
map.on('moveend zoomend', () => {
    saveMapState(map);
});

/**
 * Loads map state from localStorage
 * @returns {Object|null} Saved map state or null
 */
function loadMapState() {
    try {
        const saved = localStorage.getItem(STORAGE_KEYS.MAP_STATE);
        return saved ? JSON.parse(saved) : null;
    } catch (error) {
        console.error('Error loading map state:', error);
        return null;
    }
}

/**
 * Saves map state to localStorage
 * @param {L.Map} mapInstance - The map instance
 */
function saveMapState(mapInstance) {
    try {
        if (!mapInstance) return;
        const mapState = {
            zoom: mapInstance.getZoom(),
            center: mapInstance.getCenter()
        };
        localStorage.setItem(STORAGE_KEYS.MAP_STATE, JSON.stringify(mapState));
    } catch (error) {
        console.error('Error saving map state:', error);
    }
}

/**
 * Initializes the application
 */
async function initializeApp() {
    try {
        console.log('Initializing application...');
        
        // Check if base data already exists in IndexedDB
        const markerCount = await dataManager.getLocationMarkerCount();
        
        if (markerCount > 0) {
            console.log('Loading existing data from IndexedDB...');
            await loadExistingData();
        } else {
            console.log('No existing data found, fetching from API...');
            await loadAllLocationData();
        }
        
        // Initialize UI components
        await initializeCityFilter();
        updateCampaignUI();
        await updateStatistics();
        
        // Load saved filter state
        loadFilterState();
        
        console.log('Application initialized successfully');
        
    } catch (error) {
        console.error('Error initializing app:', error);
        // Fallback to fetching data
        try {
            await loadAllLocationData();
        } catch (fallbackError) {
            console.error('Fallback data loading also failed:', fallbackError);
            alert('Failed to initialize application. Please refresh the page.');
        }
    }
}

/**
 * Loads existing data from IndexedDB
 */
async function loadExistingData() {
    try {
        const locationMarkers = await dataManager.getAllLocationMarkers();
        allLocationData.length = 0;
        allLocationData.push(...locationMarkers);
        
        console.log(`Loaded ${locationMarkers.length} location markers from IndexedDB`);
    } catch (error) {
        console.error('Error loading existing data:', error);
        throw error;
    }
}

/**
 * Fetches and processes all location data from the API
 */
async function loadAllLocationData() {
    try {
        const locationData = await apiService.fetchAllLocationData();
        allLocationData.length = 0;
        allLocationData.push(...locationData);

        // Store data in IndexedDB
        await dataManager.addLocationMarkers(locationData.map(item => ({
            id: item.id,
            name: item.name,
            lat: item.lat,
            lng: item.lng
        })));

        console.log('All location data loaded and stored in IndexedDB');
    } catch (error) {
        console.error('Error loading location data:', error);
        throw error;
    }
}

/**
 * Loads filter state from localStorage
 */
function loadFilterState() {
    try {
        const saved = localStorage.getItem(STORAGE_KEYS.FILTER_STATE);
        if (saved) {
            const filterState = JSON.parse(saved);
            
            const greyToggle = document.getElementById('grey-markers-toggle');
            const clusteringToggle = document.getElementById('clustering-toggle');
            
            if (greyToggle) greyToggle.checked = filterState.showAll ?? true;
            if (clusteringToggle) clusteringToggle.checked = filterState.clusteringEnabled ?? true;
        }
    } catch (error) {
        console.error('Error loading filter state:', error);
    }
}

/**
 * Placeholder functions for features that need to be implemented
 * These will be moved to appropriate modules in the next phase
 */

// Placeholder for city filter manager
const cityFilterManager = {
    shouldShowMarker: (name) => true, // For now, show all markers
    extractCities: (markers) => [],
    populateDropdown: (cities) => {}
};

// Placeholder functions that need to be implemented
function initializeCityFilter() {
    // TODO: Implement city filter initialization
    console.log('City filter initialization - placeholder');
}

function updateCampaignUI() {
    // TODO: Implement campaign UI update
    console.log('Campaign UI update - placeholder');
}

function updateStatistics() {
    // TODO: Implement statistics update
    console.log('Statistics update - placeholder');
}

// Global functions that need to be accessible from HTML
window.loadCampaignData = async function(event) {
    console.log('Load campaign data - placeholder');
};

window.toggleCampaignVisibility = function(campaignId, visible) {
    console.log('Toggle campaign visibility - placeholder');
};

window.clearSpecificCampaign = function(campaignId) {
    console.log('Clear specific campaign - placeholder');
};

window.toggleClustering = function(enabled) {
    console.log('Toggle clustering - placeholder');
};

window.toggleLegend = function() {
    console.log('Toggle legend - placeholder');
};

window.toggleMinimize = function() {
    console.log('Toggle minimize - placeholder');
};

window.refreshAllLocationData = function() {
    console.log('Refresh all location data - placeholder');
};

// Initialize the application when the DOM is loaded
document.addEventListener('DOMContentLoaded', initializeApp);

// Export for use in other modules
export { 
    map, 
    apiService, 
    dataManager, 
    campaignManager,
    allLocationData,
    allCampaignData
};
