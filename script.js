/**
 * <PERSON><PERSON><PERSON>Y SCRIPT FILE - BEING REFACTORED TO ES MODULES
 *
 * This file contains the original monolithic code that is being refactored
 * into clean ES modules located in the js/ directory.
 *
 * New modular structure:
 * - js/constants.js - Application constants and configuration
 * - js/utilities.js - Utility functions
 * - js/api-service.js - API operations
 * - js/data-manager.js - Database operations
 * - js/campaign-manager.js - Campaign management
 * - js/main.js - Application entry point
 */

// Configuration Constants
const API_CONFIG = {
    BASE_URL: 'https://atlasmedia.mediani.fi/api/v1/public-map-point-markers/',
    CAMPAIGN_URL_TEMPLATE: 'https://atlasmedia.mediani.fi/api/v1/reservation-resources-map/',
    CORS_PROXY_URL: 'https://corsproxy.io/?',
    DEFAULT_LOCATION_ID: '100',
    JSON_FORMAT_PARAM: '/?format=json&page=',
    INITIAL_PAGE: 1
};

const MAP_CONFIG = {
    DEFAULT_CENTER: [62.160871, 25.6416672],
    DEFAULT_ZOOM: 8,
    DEFAULT_CLUSTER_RADIUS: 70
};

const STORAGE_KEYS = {
    CAMPAIGN_SETTINGS: 'campaignSettings',
    MAP_STATE: 'mapState',
    FILTER_STATE: 'filterState'
};

const DATABASE_CONFIG = {
    NAME: 'MainosDB',
    VERSION: 1,
    TABLES: {
        ALL_MARKERS: 'allMarkers',
        CAMPAIGN_MARKERS: 'markersCampaigns'
    }
};

const MARKER_COLORS = {
    GREY: '#7B7B7B',
    GREEN: '#2AAD27',
    RED: '#CB2B3E',
    BLUE: '#2A81CB',
    ORANGE: '#CB8427',
    YELLOW: '#CAC428',
    VIOLET: '#9C2BCB',
    GOLD: '#FFD326',
    PINK: '#e83e8c',
    CYAN: '#17a2b8',
    LIME: '#32CD32',
    BROWN: '#8B4513',
    NAVY: '#000080',
    TEAL: '#20c997',
    SILVER: '#C0C0C0',
    MAROON: '#800000',
    OLIVE: '#808000',
    AQUA: '#00FFFF',
    FUCHSIA: '#FF00FF',
    PURPLE: '#6f42c1',
    INDIGO: '#6610f2',
    CORAL: '#FF7F50',
    CRIMSON: '#DC143C',
    FOREST: '#228B22',
    ROYAL: '#4169E1',
    TOMATO: '#FF6347',
    STEEL: '#4682B4',
    // Reserved colors (not available for campaigns)
    BLACK: '#000000',
    DARK_GREY: '#6c757d'
};

const CAMPAIGN_COLORS = [
    MARKER_COLORS.RED,
    MARKER_COLORS.BLUE,
    MARKER_COLORS.ORANGE,
    MARKER_COLORS.YELLOW,
    MARKER_COLORS.VIOLET,
    MARKER_COLORS.GOLD,
    MARKER_COLORS.CYAN,
    MARKER_COLORS.PINK,
    MARKER_COLORS.TEAL,
    MARKER_COLORS.INDIGO,
    MARKER_COLORS.PURPLE,
    MARKER_COLORS.BROWN,
    MARKER_COLORS.LIME,
    MARKER_COLORS.CORAL,
    MARKER_COLORS.NAVY,
    MARKER_COLORS.MAROON,
    MARKER_COLORS.OLIVE,
    MARKER_COLORS.AQUA,
    MARKER_COLORS.FUCHSIA,
    MARKER_COLORS.SILVER
];

const ADVERTISEMENT_TYPES = {
    MAXI: 'maxi',
    CLASSIC_KESKI: 'classic_keski',
    CLASSIC_SINGLE: 'classic_single',
    DEFAULT: 'default'
};

const UI_MESSAGES = {
    CONFIRM_CLEAR_CAMPAIGN: 'Are you sure you want to clear campaign "{name}"? This action cannot be undone.',
    CONFIRM_REFRESH_DATA: 'Are you sure you want to refresh base marker data? This will clear all local data about base locations and refetch from the server.',
    CONFIRM_IMPORT_DATA: 'Are you sure you want to import data? This will completely replace all existing data. This action cannot be undone.',
    CONFIRM_OVERWRITE_CAMPAIGN: 'Campaign "{name}" (ID: {id}) already exists in the database.\nDo you want to overwrite the existing data for this campaign?',
    ERROR_LOADING_CAMPAIGN: 'Error loading campaign data. Please try again.',
    ERROR_CLEARING_CAMPAIGN: 'Error clearing campaign data. Please try again.',
    ERROR_REFRESHING_DATA: 'Error refreshing data. Please try again.',
    ERROR_IMPORTING_DATA: 'Error importing data. Please try again.',
    ERROR_EXPORTING_DATA: 'Error exporting data. Please try again.',
    CAMPAIGN_LOADED_SUCCESS: 'Campaign data loaded successfully',
    CAMPAIGN_CLEARED_SUCCESS: 'Campaign {id} cleared',
    DATA_REFRESHED_SUCCESS: 'Base marker locations refreshed successfully'
};

// Application State
let currentPage = API_CONFIG.INITIAL_PAGE;
let activeCampaignId = null;
let currentApiUrl = buildApiUrl(API_CONFIG.INITIAL_PAGE);
let currentCampaignUrl = null;

function buildApiUrl(page) {
    const baseUrl = API_CONFIG.BASE_URL + API_CONFIG.DEFAULT_LOCATION_ID + API_CONFIG.JSON_FORMAT_PARAM + page;
    return API_CONFIG.CORS_PROXY_URL + encodeURIComponent(baseUrl);
}

function buildCampaignUrl(campaignId) {
    if (!campaignId) return null;
    const baseUrl = API_CONFIG.CAMPAIGN_URL_TEMPLATE + campaignId + '/?format=json';
    return API_CONFIG.CORS_PROXY_URL + encodeURIComponent(baseUrl);
}

// Data storage arrays
const allLocationData = [];
const filteredLocationData = [];
const allCampaignData = [];
const filteredCampaignData = [];

// Initialize IndexedDB database and tables
const database = new Dexie(DATABASE_CONFIG.NAME);
database.version(DATABASE_CONFIG.VERSION).stores({
  [DATABASE_CONFIG.TABLES.ALL_MARKERS]: 'id, name, lat, lng',
  [DATABASE_CONFIG.TABLES.CAMPAIGN_MARKERS]: '[campaignId+markerId], campaignName, campaignDescription, campaignStartDate, campaignEndDate, markerName, markerLat, markerLng, markerVisited, markerDateVisited'
});

/**
 * Manages marker data operations using IndexedDB for storage.
 * Handles status tracking, timestamps, and campaign-specific data.
 */
class MarkerDataManager {
    constructor() {
        this.database = database;
    }

    /**
     * Gets the visited status for a campaign marker
     * @param {string} campaignId - The campaign ID
     * @param {number} markerId - The marker ID
     * @returns {Promise<boolean>} The visited status
     */
    async getMarkerVisitedStatus(campaignId, markerId) {
        try {
            const marker = await this.database.markersCampaigns.get([campaignId, markerId]);
            return marker ? marker.markerVisited : false;
        } catch (error) {
            console.error('Error getting marker status:', error);
            return false;
        }
    }

    /**
     * Gets the visited timestamp for a campaign marker
     * @param {string} campaignId - The campaign ID
     * @param {number} markerId - The marker ID
     * @returns {Promise<string|null>} The visited timestamp or null
     */
    async getMarkerVisitedTimestamp(campaignId, markerId) {
        try {
            const marker = await this.database.markersCampaigns.get([campaignId, markerId]);
            return marker ? marker.markerDateVisited : null;
        } catch (error) {
            console.error('Error getting marker timestamp:', error);
            return null;
        }
    }

    /**
     * Updates the visited status and timestamp for a campaign marker
     * @param {string} campaignId - The campaign ID
     * @param {number} markerId - The marker ID
     * @param {boolean} visited - The new visited status
     * @returns {Promise<void>}
     */
    async updateMarkerVisitedStatus(campaignId, markerId, isVisited) {
        try {
            const updateData = {
                markerVisited: isVisited,
                markerDateVisited: isVisited ? new Date().toISOString() : null
            };
            await this.database.markersCampaigns.update([campaignId, markerId], updateData);
        } catch (error) {
            console.error('Error updating marker status:', error);
        }
    }

    /**
     * Gets all campaign markers for a specific campaign
     * @param {string} campaignId - The campaign ID
     * @returns {Promise<Array>} Array of campaign markers
     */
    async getCampaignMarkers(campaignId) {
        try {
            return await this.database.markersCampaigns.where('campaignId').equals(campaignId).toArray();
        } catch (error) {
            console.error('Error getting campaign markers:', error);
            return [];
        }
    }

    /**
     * Gets all unique campaign IDs
     * @returns {Promise<Array>} Array of unique campaign IDs
     */
    async getAllCampaignIds() {
        try {
            const campaigns = await this.database.markersCampaigns.toArray();
            return [...new Set(campaigns.map(c => c.campaignId))];
        } catch (error) {
            console.error('Error getting campaign IDs:', error);
            return [];
        }
    }

    /**
     * Clears all campaign data for a specific campaign
     * @param {string} campaignId - The campaign ID to clear
     * @returns {Promise<void>}
     */
    async clearCampaignData(campaignId) {
        try {
            await this.database.markersCampaigns.where('campaignId').equals(campaignId).delete();
        } catch (error) {
            console.error('Error clearing campaign data:', error);
        }
    }

    /**
     * Clears all campaign data
     * @returns {Promise<void>}
     */
    async clearAllCampaignData() {
        try {
            await this.database.markersCampaigns.clear();
        } catch (error) {
            console.error('Error clearing all campaign data:', error);
        }
    }
}

const dataManager = new MarkerDataManager();

/**
 * Manages multiple campaigns with individual visibility controls and color assignments.
 */
class CampaignManager {
    constructor() {
        this.campaigns = new Map();
        this.nextColorIndex = 0;
        this.loadCampaignSettings();
    }

    /**
     * Adds a new campaign or updates an existing one
     * @param {string} campaignId - The campaign ID
     * @param {string} campaignName - The campaign name
     * @param {string} campaignDescription - The campaign description
     */
    addCampaign(campaignId, campaignName, campaignDescription = '') {
        if (!this.campaigns.has(campaignId)) {
            const assignedColor = this.getNextAvailableColor();
            this.campaigns.set(campaignId, {
                name: campaignName,
                description: campaignDescription,
                color: assignedColor,
                visible: true
            });
        } else {
            this.updateExistingCampaign(campaignId, campaignName, campaignDescription);
        }
        this.saveCampaignSettings();
    }

    updateExistingCampaign(campaignId, campaignName, campaignDescription) {
        const campaign = this.campaigns.get(campaignId);
        if (campaign) {
            campaign.name = campaignName;
            campaign.description = campaignDescription;
        }
    }

    getNextAvailableColor() {
        const color = CAMPAIGN_COLORS[this.nextColorIndex % CAMPAIGN_COLORS.length];
        this.nextColorIndex++;
        return color;
    }

    /**
     * Removes a campaign
     * @param {string} campaignId - The campaign ID to remove
     */
    removeCampaign(campaignId) {
        this.campaigns.delete(campaignId);
        this.saveCampaignSettings();
    }

    /**
     * Gets campaign color
     * @param {string} campaignId - The campaign ID
     * @returns {string} The campaign color
     */
    getCampaignColor(campaignId) {
        if (!campaignId) return MARKER_COLORS.GREY;
        const campaign = this.campaigns.get(campaignId);
        return campaign ? campaign.color : MARKER_COLORS.GREY;
    }

    /**
     * Gets campaign information
     * @param {string} campaignId - The campaign ID
     * @returns {Object|null} Campaign information or null if not found
     */
    getCampaignInfo(campaignId) {
        return this.campaigns.get(campaignId) || null;
    }

    /**
     * Checks if a campaign exists
     * @param {string} campaignId - The campaign ID
     * @returns {boolean} True if campaign exists
     */
    hasCampaign(campaignId) {
        return this.campaigns.has(campaignId);
    }

    /**
     * Gets campaign visibility
     * @param {string} campaignId - The campaign ID
     * @returns {boolean} Whether the campaign is visible
     */
    isCampaignVisible(campaignId) {
        const campaign = this.campaigns.get(campaignId);
        return campaign ? campaign.visible : true;
    }

    /**
     * Sets campaign visibility
     * @param {string} campaignId - The campaign ID
     * @param {boolean} visible - Whether the campaign should be visible
     */
    setCampaignVisibility(campaignId, isVisible) {
        const campaign = this.campaigns.get(campaignId);
        if (campaign) {
            campaign.visible = isVisible;
            this.saveCampaignSettings();
        }
    }

    /**
     * Toggles campaign visibility
     * @param {string} campaignId - The campaign ID
     * @returns {boolean} New visibility state
     */
    toggleCampaignVisibility(campaignId) {
        const campaign = this.campaigns.get(campaignId);
        if (campaign) {
            campaign.visible = !campaign.visible;
            this.saveCampaignSettings();
            return campaign.visible;
        }
        return false;
    }

    /**
     * Gets the total number of campaigns
     * @returns {number} Number of campaigns
     */
    getCampaignCount() {
        return this.campaigns.size;
    }

    /**
     * Gets all campaigns
     * @returns {Map} Map of campaign data
     */
    getAllCampaigns() {
        return this.campaigns;
    }

    /**
     * Gets visible campaigns only
     * @returns {Array} Array of visible campaign IDs
     */
    getVisibleCampaigns() {
        return Array.from(this.campaigns.entries())
            .filter(([_, campaign]) => campaign.visible)
            .map(([campaignId, _]) => campaignId);
    }

    /**
     * Saves campaign settings to localStorage
     */
    saveCampaignSettings() {
        const campaignData = {};
        this.campaigns.forEach((campaign, campaignId) => {
            campaignData[campaignId] = campaign;
        });
        localStorage.setItem(STORAGE_KEYS.CAMPAIGN_SETTINGS, JSON.stringify({
            campaigns: campaignData,
            nextColorIndex: this.nextColorIndex
        }));
    }

    /**
     * Loads campaign settings from localStorage
     */
    loadCampaignSettings() {
        const savedSettings = localStorage.getItem(STORAGE_KEYS.CAMPAIGN_SETTINGS);
        if (savedSettings) {
            try {
                const parsedData = JSON.parse(savedSettings);
                this.nextColorIndex = parsedData.nextColorIndex || 0;
                if (parsedData.campaigns) {
                    Object.entries(parsedData.campaigns).forEach(([campaignId, campaign]) => {
                        this.campaigns.set(campaignId, campaign);
                    });
                }
            } catch (error) {
                console.error('Error loading campaign settings:', error);
            }
        }
    }

    /**
     * Clears all campaign settings
     */
    clearAllCampaigns() {
        this.campaigns.clear();
        this.nextColorIndex = 0;
        this.saveCampaignSettings();
    }
}

const campaignManager = new CampaignManager();

/**
 * Handles all API operations for fetching location and campaign data
 */
class ApiService {
    constructor() {
        this.currentPage = API_CONFIG.INITIAL_PAGE;
        this.currentApiUrl = buildApiUrl(this.currentPage);
    }

    /**
     * Fetches a single page of location data from the base API
     * @returns {Promise<Object>} API response data
     * @throws {Error} When API request fails
     */
    async fetchLocationDataPage() {
        try {
            const response = await fetch(this.currentApiUrl);
            if (!response.ok) {
                throw new Error(`API request failed with status: ${response.status}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error fetching location data:', error);
            throw error;
        }
    }

    /**
     * Fetches all pages of location data recursively
     * @returns {Promise<Array>} All location data
     */
    async fetchAllLocationData() {
        const allData = [];
        let hasMorePages = true;

        while (hasMorePages) {
            try {
                const pageData = await this.fetchLocationDataPage();
                allData.push(...pageData.results);

                if (pageData.next) {
                    this.currentPage++;
                    this.currentApiUrl = buildApiUrl(this.currentPage);
                } else {
                    hasMorePages = false;
                }
            } catch (error) {
                console.error('Error fetching page data:', error);
                throw error;
            }
        }

        return allData;
    }

    /**
     * Fetches campaign data for a specific campaign ID
     * @param {string} campaignId - The campaign ID to fetch
     * @returns {Promise<Object>} Campaign data
     * @throws {Error} When campaign data fetch fails
     */
    async fetchCampaignData(campaignId) {
        if (!campaignId) {
            throw new Error('Campaign ID is required');
        }

        const campaignUrl = buildCampaignUrl(campaignId);
        if (!campaignUrl) {
            throw new Error('Failed to build campaign URL');
        }

        try {
            const response = await fetch(campaignUrl);
            if (!response.ok) {
                throw new Error(`Failed to fetch campaign data. Status: ${response.status}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error fetching campaign data:', error);
            throw error;
        }
    }

    /**
     * Resets the API service to initial state
     */
    reset() {
        this.currentPage = API_CONFIG.INITIAL_PAGE;
        this.currentApiUrl = buildApiUrl(this.currentPage);
    }
}

const apiService = new ApiService();

/**
 * Manages city filtering functionality
 */
class CityFilterManager {
    constructor() {
        this.selectedCity = '';
        this.availableCities = [];
        this.loadFilterState();
    }

    /**
     * Extracts city names from marker data using the existing logic
     * @param {Array} allMarkers - Array of all marker data
     * @returns {Array} Array of unique city names
     */
    extractCities(allMarkers) {
        const uniqueLocations = new Set();

        allMarkers.forEach(item => {
            if (typeof item.name === 'string') {
                const parts = item.name.split(' ');
                if (parts.length > 1) {
                    uniqueLocations.add(parts[1].toLowerCase());
                }
            }
        });

        return Array.from(uniqueLocations)
            .filter(Boolean)
            .map(str => str.charAt(0).toUpperCase() + str.slice(1))
            .sort();
    }

    /**
     * Populates the city filter dropdown
     * @param {Array} cities - Array of city names
     */
    populateDropdown(cities) {
        this.availableCities = cities;
        const dropdown = document.getElementById('city-filter-dropdown');
        if (!dropdown) return;

        // Clear existing options except "All Cities"
        dropdown.innerHTML = '<option value="">All Cities</option>';

        // Add city options
        cities.forEach(city => {
            const option = document.createElement('option');
            option.value = city.toLowerCase();
            option.textContent = city;
            if (city.toLowerCase() === this.selectedCity) {
                option.selected = true;
            }
            dropdown.appendChild(option);
        });
    }

    /**
     * Sets the selected city filter
     * @param {string} city - The city to filter by (lowercase)
     */
    setSelectedCity(city) {
        this.selectedCity = city;
        this.saveFilterState();
    }

    /**
     * Gets the selected city filter
     * @returns {string} The selected city (lowercase)
     */
    getSelectedCity() {
        return this.selectedCity;
    }

    /**
     * Checks if a marker should be visible based on city filter
     * @param {string} markerName - The marker name to check
     * @returns {boolean} Whether the marker should be visible
     */
    shouldShowMarker(markerName) {
        if (!this.selectedCity || !markerName) return true;

        const parts = markerName.split(' ');
        if (parts.length > 1) {
            const markerCity = parts[1].toLowerCase();
            return markerCity === this.selectedCity;
        }
        return false;
    }

    /**
     * Saves filter state to localStorage
     */
    saveFilterState() {
        localStorage.setItem('cityFilter', this.selectedCity);
    }

    /**
     * Loads filter state from localStorage
     */
    loadFilterState() {
        const saved = localStorage.getItem('cityFilter');
        this.selectedCity = saved || '';
    }

    /**
     * Clears the city filter
     */
    clearFilter() {
        this.selectedCity = '';
        this.saveFilterState();
        const dropdown = document.getElementById('city-filter-dropdown');
        if (dropdown) {
            dropdown.value = '';
        }
    }
}

const cityFilterManager = new CityFilterManager();

/**
 * Manages saving and loading user preferences (map view and filters) to/from localStorage.
 */
class UserPreferencesManager {
    /**
     * Initializes the keys used for storing data in localStorage.
     */
    constructor() {
        this.mapViewKey = STORAGE_KEYS.MAP_STATE;
        this.filtersKey = STORAGE_KEYS.FILTER_STATE;
    }

    /**
     * Saves the current map center and zoom level to localStorage.
     * @param {L.Map} map The Leaflet map instance.
     */
    saveMapState(map) {
        try {
            if (!map) {
                console.warn('Cannot save map state: map instance is null');
                return;
            }

            const mapState = {
                zoom: map.getZoom(),
                center: map.getCenter()
            };
            localStorage.setItem(this.mapViewKey, JSON.stringify(mapState));
        } catch (error) {
            console.error('Error saving map state:', error);
        }
    }

    /**
     * Loads the saved map state from localStorage.
     * @returns {Object|null} The saved map state {zoom, center} or null if not found.
     */
    loadMapState() {
        try {
            const savedState = localStorage.getItem(this.mapViewKey);
            return savedState ? JSON.parse(savedState) : null;
        } catch (error) {
            console.error('Error loading map state:', error);
            return null;
        }
    }

    /**
     * Saves the current state of UI filters (toggles, clustering) to localStorage.
     */
    saveFilterState() {
        try {
            const filterState = this.getCurrentFilterState();
            localStorage.setItem(this.filtersKey, JSON.stringify(filterState));
        } catch (error) {
            console.error('Error saving filter state:', error);
        }
    }

    /**
     * Gets the current filter state from UI elements
     * @returns {Object} Current filter state
     */
    getCurrentFilterState() {
        const greyToggle = document.getElementById('grey-markers-toggle');
        const clusteringToggle = document.getElementById('clustering-toggle');

        return {
            showAll: greyToggle ? greyToggle.checked : true,
            clusteringEnabled: clusteringToggle ? clusteringToggle.checked : true,
            clusterRadius: clusterRadius || MAP_CONFIG.DEFAULT_CLUSTER_RADIUS
        };
    }

    /**
     * Loads the saved filter state from localStorage.
     * @returns {Object} The saved filter state or a default state.
     */
    loadFilterState() {
        try {
            const savedState = localStorage.getItem(this.filtersKey);
            return savedState ? JSON.parse(savedState) : this.getDefaultFilterState();
        } catch (error) {
            console.error('Error loading filter state:', error);
            return this.getDefaultFilterState();
        }
    }

    /**
     * Gets the default filter state
     * @returns {Object} Default filter state
     */
    getDefaultFilterState() {
        return {
            showAll: true,
            clusteringEnabled: true,
            clusterRadius: MAP_CONFIG.DEFAULT_CLUSTER_RADIUS
        };
    }
}
const prefsManager = new UserPreferencesManager();

/**
 * Manages UI operations including statistics display, campaign UI, and user interactions
 */
class UIManager {
    constructor() {
        this.statisticsElements = {
            totalCount: document.getElementById('total-count'),
            visitedCount: document.getElementById('visited-count'),
            notVisitedCount: document.getElementById('not-visited-count'),
            progressPercent: document.getElementById('progress-percent'),
            progressBar: document.getElementById('progress-bar')
        };
    }

    /**
     * Updates the statistics display based on visible campaigns
     * @param {Object} stats - Statistics object with totalCount, visitedCount, notVisitedCount
     */
    updateStatisticsDisplay(stats) {
        const { totalCount, visitedCount, notVisitedCount } = stats;

        if (this.statisticsElements.totalCount) {
            this.statisticsElements.totalCount.textContent = totalCount;
        }
        if (this.statisticsElements.visitedCount) {
            this.statisticsElements.visitedCount.textContent = visitedCount;
        }
        if (this.statisticsElements.notVisitedCount) {
            this.statisticsElements.notVisitedCount.textContent = notVisitedCount;
        }

        // Update progress bar
        const progressPercent = totalCount > 0 ? Math.round((visitedCount / totalCount) * 100) : 0;
        if (this.statisticsElements.progressPercent) {
            this.statisticsElements.progressPercent.textContent = `${progressPercent}%`;
        }
        if (this.statisticsElements.progressBar) {
            this.statisticsElements.progressBar.style.width = `${progressPercent}%`;
        }
    }

    /**
     * Updates the campaign UI to show all campaigns with individual controls
     */
    updateCampaignUI() {
        const campaignInfoElement = document.getElementById('campaign-info');
        if (!campaignInfoElement) return;

        const campaigns = campaignManager.getAllCampaigns();

        if (campaigns.size === 0) {
            campaignInfoElement.style.display = 'none';
            return;
        }

        campaignInfoElement.style.display = 'block';
        let campaignHTML = '<div class="campaign-header">Campaigns:</div>';

        campaigns.forEach((campaign, campaignId) => {
            const shortId = campaignId.substring(0, 8);
            campaignHTML += this.createCampaignControlHTML(campaignId, campaign, shortId);
        });

        campaignInfoElement.innerHTML = campaignHTML;
    }

    /**
     * Creates HTML for a single campaign control
     * @param {string} campaignId - Campaign ID
     * @param {Object} campaign - Campaign data
     * @param {string} shortId - Shortened campaign ID
     * @returns {string} HTML string
     */
    createCampaignControlHTML(campaignId, campaign, shortId) {
        return `
            <div class="campaign-item" data-campaign-id="${campaignId}">
                <div class="campaign-controls">
                    <input type="checkbox"
                           id="campaign-toggle-${campaignId}"
                           ${campaign.visible ? 'checked' : ''}
                           class="checkbox-input-campaign"
                           style="accent-color: ${campaign.color};"
                           onchange="toggleCampaignVisibility('${campaignId}', this.checked)" />
                    <div class="campaign-info-text">
                        <div class="campaign-name" style="color: ${campaign.color};">
                            ${campaign.name}
                        </div>
                        <div class="campaign-id">ID: ${shortId}</div>
                    </div>
                    <button class="campaign-clear-btn"
                            style="background-color: ${campaign.color};"
                            onclick="clearSpecificCampaign('${campaignId}')">
                        Clear
                    </button>
                </div>
                ${campaign.description ? `<div class="campaign-description">${campaign.description}</div>` : ''}
            </div>
        `;
    }

    /**
     * Shows or hides the campaign info panel
     * @param {boolean} show - Whether to show the panel
     */
    toggleCampaignInfoPanel(show) {
        const campaignInfoElement = document.getElementById('campaign-info');
        if (campaignInfoElement) {
            campaignInfoElement.style.display = show ? 'block' : 'none';
        }
    }

    /**
     * Toggles the visibility of the marker legend
     */
    toggleLegend() {
        const legend = document.getElementById('marker-legend');
        if (!legend) return;

        const isVisible = legend.style.display !== 'none';
        legend.style.display = isVisible ? 'none' : 'block';
    }

    /**
     * Toggles the minimized/maximized state of the control panel
     */
    toggleMinimize() {
        const container = document.getElementById('container');
        const content = document.getElementById('tracker-content');
        const minimizeBtn = document.getElementById('minimize-btn');

        if (!container || !content || !minimizeBtn) return;

        const isMinimized = container.classList.contains('container-minimized');

        if (isMinimized) {
            container.classList.remove('container-minimized');
            content.style.display = 'block';
            minimizeBtn.innerHTML = '<i class="fas fa-minus"></i>';
        } else {
            container.classList.add('container-minimized');
            content.style.display = 'none';
            minimizeBtn.innerHTML = '<i class="fas fa-plus"></i>';
        }
    }

    /**
     * Shows a user-friendly error message
     * @param {string} message - Error message to display
     */
    showErrorMessage(message) {
        alert(message);
    }

    /**
     * Shows a confirmation dialog
     * @param {string} message - Confirmation message
     * @returns {boolean} User's choice
     */
    showConfirmDialog(message) {
        return confirm(message);
    }
}

const uiManager = new UIManager();

/**
 * Manages marker creation, styling, and icon generation
 */
class MarkerManager {
    constructor() {
        this.markerTypes = {
            MAXI: 'maxi',
            CLASSIC_KESKI: 'classic_keski',
            CLASSIC_SINGLE: 'classic_single',
            DEFAULT: 'default'
        };
    }

    /**
     * Creates a dynamic SVG marker icon with customizable color and shape
     * @param {string} color - The fill color for the marker
     * @param {string} shape - The shape type
     * @returns {L.DivIcon} A Leaflet divIcon with SVG content
     */
    createMarkerIcon(color, shape = 'default') {
        const innerShape = this.getShapeMarkup(shape);

        const svgIcon = `
            <svg width="25" height="41" viewBox="0 0 25 41" xmlns="http://www.w3.org/2000/svg">
                <path d="M12.5 0C5.6 0 0 5.6 0 12.5C0 19.4 12.5 41 12.5 41S25 19.4 25 12.5C25 5.6 19.4 0 12.5 0Z" fill="${color}" stroke="#fff" stroke-width="2"/>
                ${innerShape}
            </svg>
        `;

        return L.divIcon({
            html: svgIcon,
            className: 'custom-marker-icon',
            iconSize: [25, 41],
            iconAnchor: [12.5, 41],
            popupAnchor: [0, -41]
        });
    }

    /**
     * Gets the SVG markup for different marker shapes
     * @param {string} shape - The shape type
     * @returns {string} SVG markup for the shape
     */
    getShapeMarkup(shape) {
        switch (shape) {
            case 'circle':
            case 'maxi':
                return '<circle cx="12.5" cy="12.5" r="6" fill="#fff"/>';
            case 'rectangle':
            case 'classic_keski':
                return '<rect x="7.5" y="7.5" width="10" height="10" fill="#fff"/>';
            case 'small-circle':
            case 'classic_single':
                return '<circle cx="12.5" cy="12.5" r="4" fill="#fff"/>';
            default:
                return '<circle cx="12.5" cy="12.5" r="5" fill="#fff"/>';
        }
    }

    /**
     * Gets the appropriate marker icon based on advertisement type and color
     * @param {string} advertisementType - The type of advertisement
     * @param {string} color - The color for the marker
     * @returns {L.DivIcon} The appropriate marker icon
     */
    getMarkerIcon(advertisementType, color = MARKER_COLORS.GREY) {
        switch (advertisementType) {
            case this.markerTypes.MAXI:
                return this.createMarkerIcon(color, 'maxi');
            case this.markerTypes.CLASSIC_KESKI:
                return this.createMarkerIcon(color, 'classic_keski');
            case this.markerTypes.CLASSIC_SINGLE:
                return this.createMarkerIcon(color, 'classic_single');
            default:
                return this.createMarkerIcon(color, 'default');
        }
    }

    /**
     * Determines the advertisement type from the item name
     * @param {string} name - The name of the advertisement location
     * @returns {string} The advertisement type
     */
    getAdvertisementType(name) {
        if (typeof name !== 'string') return this.markerTypes.DEFAULT;

        const lowerName = name.toLowerCase();
        if (lowerName.includes(' maxi')) return this.markerTypes.MAXI;
        if (lowerName.includes(' keski')) return this.markerTypes.CLASSIC_KESKI;
        if (lowerName.includes(' single')) return this.markerTypes.CLASSIC_SINGLE;

        return this.markerTypes.DEFAULT;
    }

    /**
     * Creates popup content for a marker
     * @param {Object} place - The place data
     * @param {boolean} isCampaignMarker - Whether this is a campaign marker
     * @returns {string} HTML content for the popup
     */
    createPopupContent(place, isCampaignMarker = false) {
        if (isCampaignMarker) {
            return this.createCampaignPopupContent(place);
        } else {
            return this.createBasePopupContent(place);
        }
    }

    /**
     * Creates popup content for base markers
     * @param {Object} place - The place data
     * @returns {string} HTML content for the popup
     */
    createBasePopupContent(place) {
        return `
            <div class="popup-content">
                <div class="popup-title">${place.name}</div>
                <div class="popup-coordinates">
                    Lat: ${place.lat}<br>
                    Lng: ${place.lng}
                </div>
            </div>
        `;
    }

    /**
     * Creates popup content for campaign markers
     * @param {Object} place - The place data
     * @returns {string} HTML content for the popup
     */
    createCampaignPopupContent(place) {
        const visitedClass = place.markerVisited ? 'visited' : 'not-visited';
        const visitedText = place.markerVisited ? 'Visited' : 'Not Visited';

        return `
            <div class="popup-content">
                <div class="popup-title">${place.markerName}</div>
                <div class="popup-campaign">Campaign: ${place.campaignName}</div>
                <div class="popup-coordinates">
                    Lat: ${place.markerLat}<br>
                    Lng: ${place.markerLng}
                </div>
                <div class="popup-status ${visitedClass}">
                    <label>
                        <input type="checkbox"
                               ${place.markerVisited ? 'checked' : ''}
                               onchange="toggleMarkerVisited('${place.campaignId}', ${place.markerId}, this.checked)">
                        ${visitedText}
                    </label>
                </div>
                ${place.markerDateVisited ? `<div class="popup-date">Visited: ${new Date(place.markerDateVisited).toLocaleDateString()}</div>` : ''}
            </div>
        `;
    }
}

const markerManager = new MarkerManager();

/**
 * Manages map operations including marker rendering and clustering
 */
class MapManager {
    constructor(mapInstance) {
        this.map = mapInstance;
        this.markersLayer = null;
        this.clusterRadius = MAP_CONFIG.DEFAULT_CLUSTER_RADIUS;
    }

    /**
     * Sets the cluster radius for marker clustering
     * @param {number} radius - The cluster radius
     */
    setClusterRadius(radius) {
        this.clusterRadius = radius;
    }

    /**
     * Creates a new marker cluster group
     * @returns {L.MarkerClusterGroup} New cluster group
     */
    createMarkerClusterGroup() {
        return L.markerClusterGroup({
            maxClusterRadius: this.clusterRadius,
            spiderfyOnMaxZoom: true,
            showCoverageOnHover: false,
            zoomToBoundsOnClick: true
        });
    }

    /**
     * Renders all markers on the map based on current visibility settings
     */
    async renderMarkers() {
        try {
            // Remove old layer and create a new one
            if (this.markersLayer) {
                this.map.removeLayer(this.markersLayer);
            }
            this.markersLayer = this.createMarkerClusterGroup();

            const showGreyMarkers = document.getElementById('grey-markers-toggle').checked;
            const visibleCampaignIds = campaignManager.getVisibleCampaigns();
            const showCampaignMarkers = visibleCampaignIds.length > 0;

            // Get all data from IndexedDB
            const [allBaseLocations, allCampaignLocations] = await Promise.all([
                database.allMarkers.toArray(),
                database.markersCampaigns.toArray()
            ]);

            // Create a set of marker IDs that have campaign data
            const campaignMarkerIds = new Set();
            if (showCampaignMarkers && allCampaignLocations.length > 0) {
                allCampaignLocations.forEach(campaignLocation => {
                    if (visibleCampaignIds.includes(campaignLocation.campaignId)) {
                        campaignMarkerIds.add(campaignLocation.markerId);
                    }
                });
            }

            // Add grey markers if enabled
            if (showGreyMarkers) {
                this.addGreyMarkers(allBaseLocations, campaignMarkerIds, showCampaignMarkers);
            }

            // Add campaign markers if any campaigns are visible
            if (showCampaignMarkers) {
                this.addCampaignMarkers(allCampaignLocations, visibleCampaignIds);
            }

            // Add the layer to the map
            this.map.addLayer(this.markersLayer);

        } catch (error) {
            console.error('Error loading markers from IndexedDB:', error);
            // Add empty layer to prevent errors
            if (!this.markersLayer) {
                this.markersLayer = this.createMarkerClusterGroup();
            }
            this.map.addLayer(this.markersLayer);
        }
    }

    /**
     * Adds grey (base) markers to the map
     * @param {Array} allBaseLocations - All base location data
     * @param {Set} campaignMarkerIds - Set of marker IDs that have campaign data
     * @param {boolean} showCampaignMarkers - Whether campaign markers are visible
     */
    addGreyMarkers(allBaseLocations, campaignMarkerIds, showCampaignMarkers) {
        allBaseLocations.forEach(place => {
            // Skip if city filter doesn't match
            if (!cityFilterManager.shouldShowMarker(place.name)) {
                return;
            }

            // If campaign markers are visible, only show grey markers that don't have campaign data
            if (showCampaignMarkers && campaignMarkerIds.has(place.id)) {
                return;
            }

            const advertisementType = markerManager.getAdvertisementType(place.name);
            const icon = markerManager.getMarkerIcon(advertisementType, MARKER_COLORS.GREY);
            const popupContent = markerManager.createPopupContent(place, false);
            const marker = L.marker([place.lat, place.lng], { icon });
            marker.locationId = place.id;
            marker.bindPopup(popupContent);
            this.markersLayer.addLayer(marker);
        });
    }

    /**
     * Adds campaign markers to the map
     * @param {Array} allCampaignLocations - All campaign location data
     * @param {Array} visibleCampaignIds - Array of visible campaign IDs
     */
    addCampaignMarkers(allCampaignLocations, visibleCampaignIds) {
        allCampaignLocations.forEach(place => {
            // Only show markers for visible campaigns
            if (!visibleCampaignIds.includes(place.campaignId)) {
                return;
            }

            // Skip if city filter doesn't match
            if (!cityFilterManager.shouldShowMarker(place.markerName)) {
                return;
            }

            const advertisementType = markerManager.getAdvertisementType(place.markerName);
            // Use visited color if marker is visited, otherwise use campaign color
            const campaignColor = campaignManager.getCampaignColor(place.campaignId);
            const markerColor = place.markerVisited ? MARKER_COLORS.GREEN : campaignColor;
            const icon = markerManager.getMarkerIcon(advertisementType, markerColor);
            const popupContent = markerManager.createPopupContent(place, true);
            const marker = L.marker([place.markerLat, place.markerLng], { icon });
            marker.locationId = place.markerId;
            marker.campaignId = place.campaignId;
            marker.bindPopup(popupContent);
            this.markersLayer.addLayer(marker);
        });
    }

    /**
     * Updates a specific marker's appearance without re-rendering all markers
     * @param {string} campaignId - The campaign ID of the marker
     * @param {number} locationId - The ID of the location that was changed
     * @param {boolean} visited - The new visited status
     */
    async updateSpecificMarker(campaignId, locationId, visited) {
        if (!this.markersLayer) return;

        try {
            // Find the marker in the cluster layer
            const allMarkers = this.markersLayer.getLayers();
            const targetMarker = allMarkers.find(marker =>
                marker.locationId === locationId && marker.campaignId === campaignId
            );

            if (targetMarker) {
                // Get marker data to determine advertisement type
                const markerData = await dataManager.getCampaignMarkers(campaignId);
                const markerInfo = markerData.find(m => m.markerId === locationId);

                if (markerInfo) {
                    // Update the marker data
                    markerInfo.markerVisited = visited;
                    markerInfo.markerDateVisited = visited ? new Date().toISOString() : null;

                    const advertisementType = markerManager.getAdvertisementType(markerInfo.markerName);
                    const campaignColor = campaignManager.getCampaignColor(campaignId);
                    const markerColor = visited ? MARKER_COLORS.GREEN : campaignColor;
                    const newIcon = markerManager.getMarkerIcon(advertisementType, markerColor);

                    targetMarker.setIcon(newIcon);

                    // Update popup content
                    const newPopupContent = markerManager.createPopupContent(markerInfo, true);
                    targetMarker.setPopupContent(newPopupContent);
                }
            }
        } catch (error) {
            console.error('Error updating specific marker:', error);
            throw error;
        }
    }
}

/**
 * Fetches and processes all location data from the API
 * Stores the data in IndexedDB and initializes the application
 */
async function loadAllLocationData() {
    try {
        const locationData = await apiService.fetchAllLocationData();
        allLocationData.length = 0;
        allLocationData.push(...locationData);

        // Store data in IndexedDB
        await database.allMarkers.bulkAdd(locationData.map(item => ({
            id: item.id,
            name: item.name,
            lat: item.lat,
            lng: item.lng
        })));

        console.log('All location data loaded and stored in IndexedDB');
        await initializeCityFilter();
        renderMapMarkers();
    } catch (error) {
        console.error('Error loading location data:', error);
        throw error;
    }
}

// Function to test some queries from IndexedDB data
async function testQueries() {
  try {
    // Test 1: Find special type of advertisement
    const allLocationData = await database.allMarkers.toArray();
    const placesByTypeOfAdvertisement = [];

    allLocationData.forEach(item => {
      if (typeof item.name === 'string' && item.name.toLowerCase().includes(' maxi')) {
        placesByTypeOfAdvertisement.push(item.name);
      }
    });
    console.log('Places: ', placesByTypeOfAdvertisement);

    // Test 2: Find all possible locations from the name field
    if (allLocationData.length === 0) return;

    const uniqueLocations = new Set();
    allLocationData.forEach(item => {
      if (typeof item.name === 'string') {
        const parts = item.name.split(' ');
        if (parts.length > 1) {
          uniqueLocations.add(parts[1].toLowerCase());
        }
      }
    });

    const locations = Array.from(uniqueLocations)
      .filter(Boolean)
      .map(str => str.charAt(0).toUpperCase() + str.slice(1));
    console.log('Locations:', locations);
  } catch (error) {
    console.error('Error running test queries:', error);
  }
}

/**
 * Processes and stores campaign data in the database
 * @param {string} campaignId - The campaign ID to process
 */
async function processCampaignData(campaignId) {
    try {
        const campaignData = await apiService.fetchCampaignData(campaignId);
        if (!campaignData) {
            throw new Error('No campaign data received');
        }

        allCampaignData.length = 0;
        allCampaignData.push(campaignData);

        if (campaignData.reserved_resources) {
            // Clear existing data for this campaign to avoid duplicates
            await dataManager.clearCampaignData(campaignData.id);

            const campaignMarkers = extractCampaignMarkers(campaignData);

            if (campaignMarkers.length > 0) {
                await database.markersCampaigns.bulkAdd(campaignMarkers);
                console.log(`Campaign data for ${campaignData.id} added to IndexedDB (${campaignMarkers.length} markers)`);

                // Add campaign to campaign manager
                campaignManager.addCampaign(
                    campaignData.id,
                    campaignData.name,
                    campaignData.description
                );

                // Update UI and re-render markers
                updateCampaignUI();
                renderMapMarkers();
            }
        }
    } catch (error) {
        console.error('Error processing campaign data:', error);
        throw error;
    }
}

/**
 * Extracts marker data from campaign API response
 * @param {Object} campaignData - Raw campaign data from API
 * @returns {Array} Array of processed campaign markers
 */
function extractCampaignMarkers(campaignData) {
    const campaignMarkers = [];

    campaignData.reserved_resources.forEach(resource => {
        if (resource.inventory_resource && resource.inventory_resource.map_point_markers) {
            resource.inventory_resource.map_point_markers.forEach(marker => {
                campaignMarkers.push({
                    campaignId: campaignData.id,
                    markerId: marker.id,
                    markerName: marker.name,
                    markerLat: marker.lat,
                    markerLng: marker.lng,
                    campaignStartDate: resource.start_date,
                    campaignEndDate: resource.end_date,
                    campaignName: campaignData.name,
                    campaignDescription: campaignData.description,
                    markerVisited: false,
                    markerDateVisited: null
                });
            });
        }
    });

    return campaignMarkers;
}

/**
 * Handles the "Load" button click. It reads the campaign ID from the input,
 * validates it, and initiates the fetch process.
 */
async function loadCampaignData(event) {
    const inputElement = document.getElementById('campaign-id-input');
    const inputCampaignId = inputElement.value.trim();

    // Validate campaign ID input
    const validationResult = validateCampaignId(inputCampaignId);
    if (!validationResult.isValid) {
        uiManager.showErrorMessage(validationResult.message);
        return;
    }

    try {
        // Check if campaign already exists in IndexedDB
        const existingMarkers = await dataManager.getCampaignMarkers(inputCampaignId);
        if (existingMarkers.length > 0) {
            const shouldOverwrite = await confirmCampaignOverwrite(existingMarkers[0], inputCampaignId);
            if (!shouldOverwrite) {
                return;
            }
        }

        // Show loading state
        const loadButton = event.target;
        const originalText = loadButton.innerHTML;
        setLoadingState(loadButton, true);

        try {
            activeCampaignId = inputCampaignId;
            currentCampaignUrl = buildCampaignUrl(activeCampaignId);

            inputElement.value = '';

            // Wait for campaign data to fully load
            await processCampaignData(inputCampaignId);

            // Ensure statistics are updated after data loads
            await updateStatistics();

            console.log(UI_MESSAGES.CAMPAIGN_LOADED_SUCCESS);

        } finally {
            // Restore button state
            setLoadingState(loadButton, false, originalText);
        }

    } catch (error) {
        console.error('Error loading campaign data:', error);
        uiManager.showErrorMessage(UI_MESSAGES.ERROR_LOADING_CAMPAIGN);
    }
}

/**
 * Validates campaign ID input
 * @param {string} campaignId - The campaign ID to validate
 * @returns {Object} Validation result with isValid flag and message
 */
function validateCampaignId(campaignId) {
    if (!campaignId) {
        return { isValid: false, message: 'Please enter a Campaign ID' };
    }

    // Check if it's a valid UUID format (basic check)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(campaignId)) {
        return { isValid: false, message: 'Please enter a valid Campaign ID (UUID format)' };
    }

    return { isValid: true, message: '' };
}

/**
 * Confirms campaign overwrite with user
 * @param {Object} existingCampaign - Existing campaign data
 * @param {string} campaignId - Campaign ID
 * @returns {Promise<boolean>} User's choice
 */
async function confirmCampaignOverwrite(existingCampaign, campaignId) {
    const campaignName = existingCampaign.campaignName || campaignId.substring(0, 8);
    const confirmMessage = UI_MESSAGES.CONFIRM_OVERWRITE_CAMPAIGN
        .replace('{name}', campaignName)
        .replace('{id}', campaignId.substring(0, 8));

    return uiManager.showConfirmDialog(confirmMessage);
}

/**
 * Sets loading state for a button
 * @param {HTMLElement} button - The button element
 * @param {boolean} isLoading - Whether to show loading state
 * @param {string} originalText - Original button text to restore
 */
function setLoadingState(button, isLoading, originalText = '') {
    if (isLoading) {
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
        button.disabled = true;
    } else {
        button.innerHTML = originalText;
        button.disabled = false;
    }
}

// Map Initialization
const savedMapState = prefsManager.loadMapState();
var map = L.map('map').setView(
    savedMapState ? savedMapState.center : MAP_CONFIG.DEFAULT_CENTER,
    savedMapState ? savedMapState.zoom : MAP_CONFIG.DEFAULT_ZOOM
);

// Saves map state whenever the user stops moving or zooming the map.
map.on('moveend zoomend', () => {
    prefsManager.saveMapState(map);
});

// Adds the OpenStreetMap tile layer to the map.
L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
  attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
}).addTo(map);

// Initialize map manager
const mapManager = new MapManager(map);

/**
 * Utility functions for common operations
 */
class UtilityManager {
    /**
     * Formats a date string for display
     * @param {string} dateString - ISO date string
     * @returns {string} Formatted date
     */
    static formatDate(dateString) {
        if (!dateString) return 'N/A';
        try {
            const date = new Date(dateString);
            return date.toLocaleDateString();
        } catch (error) {
            console.warn('Invalid date string:', dateString);
            return 'Invalid Date';
        }
    }

    /**
     * Formats a timestamp for display
     * @param {string} timestamp - ISO timestamp string
     * @returns {string} Formatted timestamp
     */
    static formatTimestamp(timestamp) {
        if (!timestamp) return 'N/A';
        try {
            const date = new Date(timestamp);
            const day = String(date.getDate()).padStart(2, '0');
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const year = date.getFullYear();
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            return `${day}-${month}-${year} ${hours}:${minutes}`;
        } catch (error) {
            console.warn('Invalid timestamp:', timestamp);
            return 'Invalid Date';
        }
    }

    /**
     * Validates if a string is a valid UUID
     * @param {string} uuid - String to validate
     * @returns {boolean} True if valid UUID
     */
    static isValidUUID(uuid) {
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
        return uuidRegex.test(uuid);
    }

    /**
     * Safely gets a nested property from an object
     * @param {Object} obj - The object to get property from
     * @param {string} path - Dot-separated path to property
     * @param {*} defaultValue - Default value if property doesn't exist
     * @returns {*} The property value or default
     */
    static getNestedProperty(obj, path, defaultValue = null) {
        try {
            return path.split('.').reduce((current, key) => current?.[key], obj) ?? defaultValue;
        } catch (error) {
            return defaultValue;
        }
    }

    /**
     * Debounces a function call
     * @param {Function} func - Function to debounce
     * @param {number} wait - Wait time in milliseconds
     * @returns {Function} Debounced function
     */
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * Creates a deep copy of an object
     * @param {*} obj - Object to clone
     * @returns {*} Deep copy of the object
     */
    static deepClone(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => this.deepClone(item));
        if (typeof obj === 'object') {
            const clonedObj = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = this.deepClone(obj[key]);
                }
            }
            return clonedObj;
        }
        return obj;
    }
}


/**
 * Finds a marker by its ID and opens its popup, zooming to it if necessary.
 * This is useful for re-opening a popup after re-rendering the map.
 * @param {number} locationId - The ID of the location whose popup should be opened.
 */
function reopenPopup(locationId) {
    if (!markersLayer) return;

    // Use getLayers() to access all markers, even those inside clusters
    const allMarkers = markersLayer.getLayers();
    const targetMarker = allMarkers.find(m => m.locationId === locationId);

    if (targetMarker) {
        // This function will zoom to the cluster and execute the callback
        markersLayer.zoomToShowLayer(targetMarker, function() {
            targetMarker.openPopup();
        });
    }
}

/**
 * Toggles the visited status of a marker
 * @param {string} campaignId - The campaign ID of the marker
 * @param {number} locationId - The ID of the location
 * @param {boolean} isVisited - The new visited status
 */
async function toggleMarkerVisited(campaignId, locationId, isVisited) {
    try {
        // Validate inputs
        if (!campaignId || !locationId) {
            throw new Error('Invalid campaign ID or location ID');
        }

        // Update the marker status in IndexedDB
        await dataManager.updateMarkerVisitedStatus(campaignId, locationId, isVisited);

        // Update only the specific marker instead of re-rendering all markers
        await updateSpecificMarker(campaignId, locationId, isVisited);

        // Update statistics
        await updateStatistics();

        console.log(`Marker ${locationId} in campaign ${campaignId} marked as ${isVisited ? 'visited' : 'not visited'}`);

    } catch (error) {
        console.error('Error toggling marker visited status:', error);
        uiManager.showErrorMessage('Error updating marker status. Please try again.');
    }
}

/**
 * Handles the change event of the "Visited" checkbox in a marker's popup.
 * It updates the location's status and only re-renders the specific marker.
 * @param {string} campaignId - The campaign ID of the marker
 * @param {number} locationId - The ID of the location that was changed.
 * @param {HTMLInputElement} checkbox - The checkbox element that was clicked.
 */
async function handleStatusChange(campaignId, locationId, checkbox) {
    await toggleMarkerVisited(campaignId, locationId, checkbox.checked);
}

/**
 * Updates a specific marker's appearance without re-rendering all markers
 * @param {string} campaignId - The campaign ID of the marker
 * @param {number} locationId - The ID of the location that was changed
 * @param {boolean} visited - The new visited status
 */
async function updateSpecificMarker(campaignId, locationId, visited) {
    try {
        await mapManager.updateSpecificMarker(campaignId, locationId, visited);
    } catch (error) {
        console.error('Error updating specific marker:', error);
        // Fallback to full re-render if specific update fails
        await renderMapMarkers();
    }
}

// Use markerManager.getAdvertisementType() instead

// Use markerManager.createMarkerIcon() instead

// Use markerManager.getMarkerIcon() instead

/**
 * Creates a marker icon for campaign layers with custom color
 * This function can be used for different campaign IDs
 * @param {string} campaignId - Campaign ID
 * @param {string} campaignType - The type of campaign ('ID1', 'ID2', etc.)
 * @returns {L.DivIcon} The appropriate campaign marker icon
 */
function getCampaignMarkerIcon(campaignId, campaignType = 'default') {
  const campaignColors = {
    'default': MARKER_COLORS.RED
  };

  const color = campaignColors[campaignType] || MARKER_COLORS.RED;
  return getMarkerIcon(campaignId, color);
}

/**
 * Helper function to determine campaign type based on campaign data
 * This can be extended in the future to support different campaign classifications
 * @param {Object} campaignData - The campaign data object
 * @returns {string} The campaign type
 */
function getCampaignType(campaignData) {
  // Future implementation could check campaign properties like:
  // - campaignData.tier (premium, standard, basic)
  // - campaignData.partnership_level
  // - campaignData.subscription_type
  // For now, return 'default' for all companies

  // Suppress unused parameter warning for future implementation
  void campaignData;
  return 'default';
}

// Global variables for map layer and settings
let markersLayer; // Holds the marker cluster layer
let clusterRadius = 70; // Default clustering radius
let userLocationMarker = null; // To hold the marker for the user's position

/**
 * Toggles clustering on/off and re-renders the map.
 * @param {boolean} enabled - Whether clustering should be enabled.
 */
function toggleClustering(enabled) {
    try {
        const radius = enabled ? MAP_CONFIG.DEFAULT_CLUSTER_RADIUS : 0;
        mapManager.setClusterRadius(radius);
        prefsManager.saveFilterState();
        renderMapMarkers();
    } catch (error) {
        console.error('Error toggling clustering:', error);
        uiManager.showErrorMessage('Error updating clustering settings. Please try again.');
    }
}

/**
 * Legacy function for backward compatibility with slider input.
 * @param {string} value - The new radius value from the slider.
 */
function updateClusterRadius(value) {
    try {
        const radius = validateClusterRadius(value);
        if (radius !== null) {
            mapManager.setClusterRadius(radius);
            prefsManager.saveFilterState();
            renderMapMarkers();
        }
    } catch (error) {
        console.error('Error updating cluster radius:', error);
        uiManager.showErrorMessage('Error updating cluster radius. Please try again.');
    }
}

/**
 * Validates and parses cluster radius value
 * @param {string|number} value - The radius value to validate
 * @returns {number|null} Validated radius or null if invalid
 */
function validateClusterRadius(value) {
    const radius = parseInt(value);
    if (isNaN(radius) || radius < 0 || radius > 200) {
        console.warn('Invalid cluster radius value:', value);
        return null;
    }
    return radius;
}

// Use UtilityManager.formatTimestamp() instead

// Use markerManager.createPopupContent() instead

/**
 * The main rendering function. It clears existing markers from the map and adds new ones
 * Implements visibility logic:
 * - If only grey markers are active - show them
 * - If grey and campaign markers with the same ID are active, campaign markers are visible, but grey for the same ID marker - not
 * - If for markerID active several campaigns - show all of them
 */
async function renderMapMarkers() {
    await mapManager.renderMarkers();
}

/**
 * Updates the statistics display based on visible campaigns only
 */
async function updateStatistics() {
    try {
        const showGreyMarkers = document.getElementById('grey-markers-toggle').checked;
        const showCampaignMarkers = campaignManager.getVisibleCampaigns().length > 0;

        let totalCount = 0;
        let visitedCount = 0;
        let notVisitedCount = 0;

        // Count grey markers if they're visible and no campaigns are visible
        if (showGreyMarkers) {
            const allBaseLocations = await database.allMarkers.toArray();
            const visibleCampaignIds = campaignManager.getVisibleCampaigns();

            if (!showCampaignMarkers || visibleCampaignIds.length === 0) {
                // Show all grey markers (filtered by city)
                const filteredGreyMarkers = allBaseLocations.filter(marker =>
                    cityFilterManager.shouldShowMarker(marker.name)
                );
                totalCount += filteredGreyMarkers.length;
                notVisitedCount += filteredGreyMarkers.length;
            } else {
                // Show only grey markers that don't have campaign data (filtered by city)
                const allCampaignLocations = await database.markersCampaigns.toArray();
                const campaignMarkerIds = new Set(allCampaignLocations.map(c => c.markerId));

                const greyOnlyMarkers = allBaseLocations.filter(marker =>
                    !campaignMarkerIds.has(marker.id) &&
                    cityFilterManager.shouldShowMarker(marker.name)
                );
                totalCount += greyOnlyMarkers.length;
                notVisitedCount += greyOnlyMarkers.length;
            }
        }

        // Count campaign markers if they're visible
        if (showCampaignMarkers) {
            const visibleCampaignIds = campaignManager.getVisibleCampaigns();
            if (visibleCampaignIds.length > 0) {
                const allCampaignLocations = await database.markersCampaigns.toArray();
                const visibleCampaignMarkers = allCampaignLocations.filter(marker =>
                    visibleCampaignIds.includes(marker.campaignId) &&
                    cityFilterManager.shouldShowMarker(marker.markerName)
                );

                totalCount += visibleCampaignMarkers.length;
                const campaignVisited = visibleCampaignMarkers.filter(m => m.markerVisited).length;
                visitedCount += campaignVisited;
                notVisitedCount += (visibleCampaignMarkers.length - campaignVisited);
            }
        }

        // Update UI using UIManager
        uiManager.updateStatisticsDisplay({ totalCount, visitedCount, notVisitedCount });

    } catch (error) {
        console.error('Error updating statistics:', error);
    }
}

/**
 * Applies city filter and re-renders the map
 * @param {string} cityValue - The selected city value from dropdown
 */
function applyCityFilter(cityValue) {
    cityFilterManager.setSelectedCity(cityValue);
    renderMapMarkers();
    updateStatistics();
}

/**
 * Initializes the city filter dropdown with available cities
 */
async function initializeCityFilter() {
    try {
        const allMarkers = await database.allMarkers.toArray();
        const cities = cityFilterManager.extractCities(allMarkers);
        cityFilterManager.populateDropdown(cities);
    } catch (error) {
        console.error('Error initializing city filter:', error);
    }
}

/**
 * A helper function to save the current filter state and then re-render the map.
 * Used as an onchange handler for filter toggles.
 */
function saveStateAndRender() {
    prefsManager.saveFilterState();
    renderMapMarkers();
    updateStatistics();
}

/**
 * Updates the campaign UI to show all campaigns with individual controls
 */
function updateCampaignUI() {
    uiManager.updateCampaignUI();
}

/**
 * Toggles the visibility of a specific campaign
 * @param {string} campaignId - The campaign ID
 * @param {boolean} visible - Whether the campaign should be visible
 */
function toggleCampaignVisibility(campaignId, visible) {
  campaignManager.setCampaignVisibility(campaignId, visible);
  renderMapMarkers();
  updateStatistics();
}

/**
 * Clears a specific campaign
 * @param {string} campaignId - The campaign ID to clear
 */
async function clearSpecificCampaign(campaignId) {
    const campaignInfo = campaignManager.getCampaignInfo(campaignId);
    const campaignName = campaignInfo ? campaignInfo.name : campaignId.substring(0, 8);

    const confirmMessage = UI_MESSAGES.CONFIRM_CLEAR_CAMPAIGN
        .replace('{name}', campaignName);

    if (!uiManager.showConfirmDialog(confirmMessage)) {
        return;
    }

    try {
        await dataManager.clearCampaignData(campaignId);
        campaignManager.removeCampaign(campaignId);
        updateCampaignUI();
        renderMapMarkers();
        updateStatistics();
        console.log(UI_MESSAGES.CAMPAIGN_CLEARED_SUCCESS.replace('{id}', campaignId));
    } catch (error) {
        console.error('Error clearing specific campaign:', error);
        uiManager.showErrorMessage(UI_MESSAGES.ERROR_CLEARING_CAMPAIGN);
    }
}

/**
 * Clears all campaigns
 */
async function clearAllCampaigns() {
  try {
    await dataManager.clearAllCampaignData();
    campaignManager.clearAllCampaigns();
    updateCampaignUI();
    renderMapMarkers();
    updateStatistics();
    console.log('All campaigns cleared');
  } catch (error) {
    console.error('Error clearing all campaigns:', error);
    alert('Error clearing campaign data. Please try again.');
  }
}

/**
 * Refreshes all marker data by clearing local storage and refetching from API
 */
async function refreshAllLocationData() {
    if (!confirm(UI_MESSAGES.CONFIRM_REFRESH_DATA)) {
        return;
    }

    try {
        // Clear table allMarkers from IndexedDB
        await database.allMarkers.clear();

        // Clear arrays
        allLocationData.length = 0;
        filteredLocationData.length = 0;

        // Reset API service
        apiService.reset();

        // Refetch data from API
        await loadAllLocationData();
        updateStatistics();

        console.log(UI_MESSAGES.DATA_REFRESHED_SUCCESS);
    } catch (error) {
        console.error('Error refreshing data:', error);
        alert(UI_MESSAGES.ERROR_REFRESHING_DATA);
    }
}

/**
 * Toggles the visibility of the marker legend
 */
function toggleLegend() {
    uiManager.toggleLegend();
    // Initialize legend markers if showing
    const legend = document.getElementById('marker-legend');
    if (legend && legend.style.display !== 'none') {
        initializeLegendMarkers();
    }
}

/**
 * Initializes the legend markers with proper icons
 */
function initializeLegendMarkers() {
  const markerTypes = ['maxi', 'classic_keski', 'classic_single'];

  markerTypes.forEach(type => {
    const legendElement = document.getElementById(`legend-${type.replace('_', '-')}`);
    if (legendElement) {
      const icon = getMarkerIcon(type, MARKER_COLORS.GREY);
      // Set the icon HTML
      legendElement.innerHTML = icon.options.html;
      // Find the SVG and resize it for the legend
      const svg = legendElement.querySelector('svg');
      if (svg) {
        svg.setAttribute('width', '15');
        svg.setAttribute('height', '25');
      }
    }
  });
}

// Use UtilityManager.formatDate() instead

/**
 * Clears all campaign data and updates the UI
 */
async function clearCampaignData() {
  try {
    await dataManager.clearAllCampaignData();

    // Hide campaign info panel
    const campaignInfoElement = document.getElementById('campaign-info');
    if (campaignInfoElement) {
      campaignInfoElement.style.display = 'none';
    }

    // Re-render the map
    renderMapMarkers();

    console.log('All campaign data cleared');
  } catch (error) {
    console.error('Error clearing campaign data:', error);
    alert('Error clearing campaign data. Please try again.');
  }
}

/**
 * Toggles the minimized/maximized state of the control panel.
 */
function toggleMinimize() {
    uiManager.toggleMinimize();
}

/**
 * Attempts to get the user's current physical location using the browser's Geolocation API.
 */
function locateUser() {
    if (!navigator.geolocation) {
        alert("Geolocation is not supported by your browser.");
        return;
    }

    // Success callback function
    function success(position) {
        const lat = position.coords.latitude;
        const lng = position.coords.longitude;
        const userLatLng = L.latLng(lat, lng);

        // Add or update the user's location marker on the map
        if (userLocationMarker) {
            userLocationMarker.setLatLng(userLatLng);
        } else {
            // Create a unique marker for the user's location (e.g., a blue circle)
            userLocationMarker = L.circleMarker(userLatLng, {
                radius: 8,
                color: '#1d6ef7',
                fillColor: '#1d6ef7',
                fillOpacity: 0.5
            }).addTo(map);
        }
        
        userLocationMarker.bindPopup("<b>You are here</b>").openPopup();

        // Center the map on the user's location with a suitable zoom level
        map.setView(userLatLng, 20);
    }

    // Error callback function
    function error(err) {
        let message = "Could not get your location. ";
        switch (err.code) {
            case err.PERMISSION_DENIED:
                message += "You denied the request for Geolocation.";
                break;
            case err.POSITION_UNAVAILABLE:
                message += "Location information is unavailable.";
                break;
            case err.TIMEOUT:
                message += "The request to get user location timed out.";
                break;
            default:
                message += "An unknown error occurred.";
                break;
        }
        alert(message);
    }

    // Request the user's location
    navigator.geolocation.getCurrentPosition(success, error);
}

/**
 * Initializes the application on page load. It restores filter states and
 * either fetches initial data or renders the map using data from IndexedDB.
 */
async function initializeApp() {
  const savedFilters = prefsManager.loadFilterState();
  document.getElementById('grey-markers-toggle').checked = savedFilters.showAll;

  // Restore clustering toggle state
  const clusteringToggle = document.getElementById('clustering-toggle');
  const clusteringEnabled = savedFilters.clusteringEnabled !== undefined ? savedFilters.clusteringEnabled : true;
  clusteringToggle.checked = clusteringEnabled;
  clusterRadius = clusteringEnabled ? 50 : 0;

  try {
    // Check if base data already exists in IndexedDB
    const markerCount = await database.allMarkers.count();

    if (markerCount > 0) {
      // Check for existing campaign data and sync with campaign manager
      const campaignIds = await dataManager.getAllCampaignIds();
      if (campaignIds.length > 0) {
        // Sync existing campaigns with campaign manager
        for (const campaignId of campaignIds) {
          const campaignMarkers = await dataManager.getCampaignMarkers(campaignId);
          if (campaignMarkers.length > 0) {
            const firstMarker = campaignMarkers[0];
            campaignManager.addCampaign(
              campaignId,
              firstMarker.campaignName,
              firstMarker.campaignDescription
            );
          }
        }
        updateCampaignUI();
      } else {
        // Hide campaign info panel if no campaign data
        const campaignInfoElement = document.getElementById('campaign-info');
        if (campaignInfoElement) campaignInfoElement.style.display = 'none';
      }

      // Initialize city filter and render the map with existing data
      await initializeCityFilter();
      renderMapMarkers();
      updateStatistics();
    } else {
      // If no data exists, fetch it from the API
      await loadAllLocationData();
    }
  } catch (error) {
    console.error('Error initializing app:', error);
    // Fallback to fetching data
    await loadAllLocationData();
  }
}

/**
 * Exports all marker data to a JSON file
 */
async function exportData() {
  try {
    const [allMarkers, campaignMarkers] = await Promise.all([
      database.allMarkers.toArray(),
      database.markersCampaigns.toArray()
    ]);

    const exportData = {
      allMarkers,
      campaignMarkers,
      exportDate: new Date().toISOString()
    };

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);

    const link = document.createElement('a');
    link.href = url;
    // link.download = `mainos-data-${new Date().toISOString().split('T')[0]}.json`;
    const now = new Date();
    const dateStr = now.toISOString().split('T')[0];
    const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '-');
    link.download = `mainos-data-${dateStr}_time_${timeStr}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    console.log('Data exported successfully');
  } catch (error) {
    console.error('Error exporting data:', error);
    alert('Error exporting data. Please try again.');
  }
}

/**
 * Imports marker data from a JSON file
 */
async function importData(event) {
  const file = event.target.files[0];
  if (!file) return;

  if (!confirm('Are you sure you want to import data? This will completely replace all existing data. This action cannot be undone.')) {
    event.target.value = '';
    return;
  }

  try {
    const text = await file.text();
    const importData = JSON.parse(text);

    // Validate the import data structure
    if (!importData.allMarkers && !importData.campaignMarkers) {
      throw new Error('Invalid file format: No marker data found');
    }

    // Clear all existing data first
    await database.allMarkers.clear();
    await database.markersCampaigns.clear();
    campaignManager.clearAllCampaigns();

    // Clear in-memory arrays
    allLocationData.length = 0;
    filteredLocationData.length = 0;
    allCampaignData.length = 0;
    filteredCampaignData.length = 0;

    // Import new data
    if (importData.allMarkers && importData.allMarkers.length > 0) {
      await database.allMarkers.bulkAdd(importData.allMarkers);
      allLocationData.push(...importData.allMarkers);
    }

    if (importData.campaignMarkers && importData.campaignMarkers.length > 0) {
      await database.markersCampaigns.bulkAdd(importData.campaignMarkers);

      // Rebuild campaign manager from imported data
      const campaignIds = [...new Set(importData.campaignMarkers.map(m => m.campaignId))];
      for (const campaignId of campaignIds) {
        const campaignMarkers = importData.campaignMarkers.filter(m => m.campaignId === campaignId);
        if (campaignMarkers.length > 0) {
          const firstMarker = campaignMarkers[0];
          campaignManager.addCampaign(
            campaignId,
            firstMarker.campaignName,
            firstMarker.campaignDescription
          );
        }
      }
    }

    // Reinitialize the application state
    await initializeCityFilter();
    updateCampaignUI();
    renderMapMarkers();
    updateStatistics();

    console.log('Data imported successfully');
    alert('Data imported successfully!');
  } catch (error) {
    console.error('Error importing data:', error);
    alert('Error importing data. Please check the file format.');
  }

  // Clear the file input
  event.target.value = '';
}

// Initialize the application when the page loads
document.addEventListener('DOMContentLoaded', () => {
  initializeApp();

  // Prevent map scroll when scrolling inside tracker-content
  const tracker = document.getElementById('tracker-content');
  if (tracker) {
    tracker.addEventListener('touchmove', function(e) {
      e.stopPropagation();
    }, { passive: false });

    tracker.addEventListener('wheel', function(e) {
      e.stopPropagation();
    }, { passive: false });
  }
});